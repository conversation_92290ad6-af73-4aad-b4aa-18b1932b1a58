import { type Group } from '@g17eco/core';
import { type MetricGroupWithSubgroups } from '../models/metricGroup';
import { ObjectId } from 'bson';

export const isMetricGroup = (group: Group | MetricGroupWithSubgroups): group is MetricGroupWithSubgroups => {
  return '_id' in group && 'groupName' in group;
};

export function getGroupCode(group: Group | MetricGroupWithSubgroups): string {
  return isMetricGroup(group) ? String(group._id) : group.code;
}

export const getSubGroup = <T extends Group | MetricGroupWithSubgroups>(group: T, code: string): T | undefined => {
  const subGroup = group.subgroups?.find((g) => getGroupCode(g) === code);
  if (!subGroup) {
    return;
  }

  return subGroup as T;
};

export const findGroupByChildCode = <T extends Group | MetricGroupWithSubgroups>(
  group: T,
  code: string
): T | undefined => {
  if (!group.subgroups) {
    return undefined;
  }

  for (const groupElement of group.subgroups) {
    if (getGroupCode(groupElement) === code || findGroupByChildCode(groupElement, code)) {
      return groupElement as T;
    }
  }
};

// type GroupChildrenType<T> = T extends MetricGroupWithSubgroups ? ObjectId[] : string[];

// export function getGroupChildrenTags<T extends Group | MetricGroupWithSubgroups>(
//   group: T
// ): GroupChildrenType<T> {
//   if (isMetricGroup(group)) {
//     return collectTags(group, '_id') as GroupChildrenType<T>;
//   }
//   return collectTags(group, 'code') as GroupChildrenType<T>;
// }

export function getGroupChildrenTags(group: MetricGroupWithSubgroups): MetricGroupWithSubgroups['_id'][];
export function getGroupChildrenTags(group: Group): Group['code'][];
export function getGroupChildrenTags(group: Group | MetricGroupWithSubgroups): MetricGroupWithSubgroups['_id'][] | Group['code'][] {
  if (isMetricGroup(group)) {
    return collectTags(group, '_id');
  }
  return collectTags(group, 'code');
}

function collectTags<T extends { subgroups?: T[] }, K extends keyof T>(group: T, key: K): T[K][] {
  const result: T[K][] = [];
  if (group.subgroups) {
    for (const sg of group.subgroups) {
      result.push(sg[key], ...collectTags(sg, key));
    }
  }
  return result;
}
