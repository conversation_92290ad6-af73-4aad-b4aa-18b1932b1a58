import moment from 'moment';
import { type Scope, type SurveyModelPlain, type SurveyWithInitiative } from '../models/survey';
import { DataPeriods } from '../service/utr/constants';
import { customDateFormat, DateFormat } from './date';
import { AssessmentType } from '../types/materiality-assessment';
import { type RoleMap, type SurveyUserRoles } from '../types/roles';
import { type UserMin } from '../models/user';
import { type BaseScopeChange } from '../service/survey/model/DelegationScope';

export enum SurveyType {
  Default = 'default',
  Aggregation = 'aggregation',
  Materiality = 'materiality',
  AutoAggregation = 'auto_aggregation',
}

export const isAggregatedSurvey = (type: unknown): type is SurveyType.Aggregation | SurveyType.AutoAggregation =>
  [SurveyType.Aggregation, SurveyType.AutoAggregation].includes(type as SurveyType);

export const isMaterialitySurvey = (survey: Pick<SurveyModelPlain, 'type'>) => {
  return survey.type === SurveyType.Materiality;
};

export const isDoubleMaterialitySurvey = (survey: Pick<SurveyModelPlain, 'type' | 'assessmentType'>) => {
  return isMaterialitySurvey(survey) && survey.assessmentType === AssessmentType.DoubleMateriality;
};

export const getSurveyDateRange = ({
  effectiveDate,
  period,
}: {
  effectiveDate: string | Date;
  period: DataPeriods;
}) => {
  const startDate = moment(effectiveDate)
    .subtract(period === DataPeriods.Yearly ? 11 : period === DataPeriods.Quarterly ? 2 : 0, 'months')
    .startOf('month')
    .startOf('day')
    .toDate();
  const endDate = moment(effectiveDate).endOf('month').endOf('day').toDate();
  return { startDate, endDate };
};

export const DATA_PERIODS_UI_MAP = {
  [DataPeriods.Monthly]: 'Monthly',
  [DataPeriods.Quarterly]: 'Quarterly',
  [DataPeriods.Yearly]: 'Annual',
} as const;


export const getReportName = (survey: Pick<SurveyWithInitiative, 'initiative' | 'period' | 'effectiveDate'>) => {
  const reportName = `${survey.initiative.name} ${
    DATA_PERIODS_UI_MAP[survey.period ?? DataPeriods.Yearly]
  } ${customDateFormat(survey.effectiveDate, DateFormat.MonthYear)} Report`;
  return reportName;
};

export const getReportingDateStart = (survey: Pick<SurveyModelPlain, 'effectiveDate' | 'period'>) => {
  const date = survey.effectiveDate;
  const period = survey.period;

  switch (period) {
    case DataPeriods.Monthly:
      return moment.utc(date).startOf('month').format(DateFormat.MonthYear);
    case DataPeriods.Quarterly:
      return moment.utc(date).subtract(2, 'months').startOf('month').format(DateFormat.MonthYear);
    case DataPeriods.Yearly:
    default:
      return moment.utc(date).subtract(11, 'months').startOf('month').format(DateFormat.MonthYear);
  }
};

export const createSurveyUsersRoles = (user: UserMin, roleMap: RoleMap) => {
  const roles: SurveyUserRoles[] = [];

  Object.entries(roleMap).forEach(([role, ids]) => {
    if (ids.includes(String(user._id))) {
      roles.push(role as SurveyUserRoles);
    }
  });

  return roles;
};

export const getGroupCodesFromScopeGroups = (
  scopeGroups: BaseScopeChange['scopeGroups'],
  scopeType: keyof Scope = 'custom'
): string[] => {
  return scopeGroups.filter((g) => g.scopeType === scopeType).map((g) => g.code);
};
