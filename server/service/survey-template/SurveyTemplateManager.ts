import { UserModel } from '../../models/user';
import { getRootInitiativeService, RootInitiativeService } from '../organization/RootInitiativeService';
import Survey, { BulkSurveysData, Scope, SurveyModelMinData, SurveyType } from '../../models/survey';
import { InitiativePlain } from '../../models/initiative';
import { SurveyTemplate, SurveyTemplateMinData, SurveyTemplateModel } from '../../models/surveyTemplate';
import { SurveyImporter } from '../survey/SurveyImporter';
import { getSelfOnboardingManager, SelfOnboardingManager } from '../onboarding/SelfOnboardingManager';
import { wwgLogger } from '../wwgLogger';
import { Logger } from 'winston';
import { HttpErrorMessages } from '../../error/ErrorMessages';
import { processScopeChange } from '../survey/surveyDelegationProcess';
import { BaseScopeChange } from '../survey/model/DelegationScope';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import { ObjectId } from 'bson';
import { SurveyUpdateData, TemplateFormData } from './types';
import moment from 'moment';
import { SurveyScope } from '../survey/SurveyScope';
import { createSurveyCode } from '../../util/string';
import { UtrvType } from '../utr/constants';
import { createStakeholderGroup } from '../stakeholder/StakeholderGroupManager';
import { getNotificationManager, NotificationManager } from '../notification/NotificationManager';
import { SurveyTemplateHistoryModel } from '../../models/surveyTemplateHistory';
import { getNameFromMergeTags } from './utils';
import { getTemplateManager, TemplateManager } from './TemplateManager';
import { getSurveyDeadlineService, SurveyDeadlineService } from '../survey/SurveyDeadline';
import { FeatureCode } from "@g17eco/core";
import { SurveyConfigService } from '../initiative/SurveyConfigService';
import { UnitConfig } from '../units/unitTypes';
import ContextError from '../../error/ContextError';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getGroupCodesFromScopeGroups } from '../../util/survey';

export class SurveyTemplateManager {
  constructor(
    private rootService: RootInitiativeService,
    private selfOnboardingManager: SelfOnboardingManager,
    private logger: Logger,
    private notificationManager: NotificationManager,
    private templateManager: TemplateManager,
    private surveyDeadlineService: SurveyDeadlineService,
    private surveyConfigService: typeof SurveyConfigService
  ) {}

  public async createTemplate(initiative: InitiativePlain, data: TemplateFormData) {
    const rootConfig = await this.rootService.getConfig(initiative, { domain: data.domain });
    const hasVerificationFeature = this.rootService.hasFeature(rootConfig, FeatureCode.Verification);

    const createData: SurveyTemplateMinData = {
      type: SurveyType.Default,
      scope: await this.selfOnboardingManager.updateSurveyScope(data, rootConfig),
      name: data.name,
      unitConfig: { ...SurveyImporter.createUnitConfig(initiative), ...data.unitConfig },
      useInitiativeSettings: Boolean(data.useInitiativeSettings),
      initiativeId: initiative._id,
      evidenceRequired: Boolean(data.evidenceRequired),
      noteRequired: Boolean(data.noteRequired),
      noteInstructions: data.noteInstructions,
      noteInstructionsEditorState: data.noteInstructionsEditorState,
      isPrivate: Boolean(data.isPrivate),
      verificationRequired: hasVerificationFeature && Boolean(data.verificationRequired),
      sourceName: DefaultBlueprintCode,
    };

    return SurveyTemplate.create(createData);
  }

  public async updateTemplate(template: SurveyTemplateModel, user: UserModel, data: SurveyUpdateData) {
    if (!template || typeof data !== 'object') {
      throw new Error(HttpErrorMessages.BadRequest);
    }

    const whitelistData: Partial<SurveyUpdateData> = {
      name: data.name ?? template.name,
      evidenceRequired: data.evidenceRequired ?? template.evidenceRequired,
      noteRequired: data.noteRequired ?? template.noteRequired,
      noteInstructions: data.noteInstructions ?? template.noteInstructions,
      noteInstructionsEditorState: data.noteInstructionsEditorState ?? template.noteInstructionsEditorState,
      verificationRequired: data.verificationRequired ?? template.verificationRequired,
      isPrivate: data.isPrivate ?? template.isPrivate,
      useInitiativeSettings: data.useInitiativeSettings ?? template.useInitiativeSettings,
    };

    if (data.unitConfig !== undefined) {
      whitelistData.unitConfig = data.unitConfig;
    }

    template.set(whitelistData);

    if (template.isModified()) {
      this.logger.info(`User ${user._id} updated template ${template._id}`);
    }

    await template.save();

    return template;
  }

  public async updateScope(template: SurveyTemplateModel, user: UserModel, data: BaseScopeChange) {
    const metricGroupIds = getGroupCodesFromScopeGroups(data.scopeGroups);
    const metricGroups =
      metricGroupIds.length > 0
        ? await InitiativeRepository.getInitiativeMetricGroups(template.initiativeId, metricGroupIds)
        : [];
    template.scope = processScopeChange({ data, surveyScope: template.scope, metricGroups });
    return template.save();
  }

  public toScope(scope?: Scope<string> | Scope): Scope {
    return {
      sdg: scope?.sdg ?? [],
      frameworks: scope?.frameworks ?? [],
      materiality: scope?.materiality ?? [],
      standards: scope?.standards ?? [],
      custom: scope?.custom?.map((id) => new ObjectId(id)) ?? [],
    };
  }

  private async createSurveyFromTemplate({
    user,
    reportingCompany,
    data,
    unitConfig,
  }: {
    user: UserModel;
    reportingCompany: InitiativePlain;
    data: BulkSurveysData;
    unitConfig: UnitConfig;
  }) {
    const effectiveDate = moment(data.effectiveDate).toDate();
    const hasVerificationFeature = await this.rootService.hasConfigFeature(reportingCompany, FeatureCode.Verification);

    const createData: SurveyModelMinData = {
      type: SurveyType.Default,
      scope: data.scope ?? SurveyScope.createEmpty(),
      visibleUtrvs: [],
      code: createSurveyCode(reportingCompany.code),
      name: getNameFromMergeTags(data, reportingCompany),
      sourceName: data.sourceName ?? DefaultBlueprintCode,
      period: data.period,
      effectiveDate: effectiveDate,
      utrvType: UtrvType.Actual,
      visibleStakeholders: [user._id],
      unitConfig,
      initiativeId: reportingCompany._id,
      stakeholders: createStakeholderGroup(),
      roles: { admin: [user._id], viewer: [] },
      evidenceRequired: Boolean(data.evidenceRequired),
      noteRequired: Boolean(data.noteRequired),
      noteInstructions: data.noteInstructions,
      noteInstructionsEditorState: data.noteInstructionsEditorState,
      verificationRequired: hasVerificationFeature && Boolean(data.verificationRequired),
      isPrivate: Boolean(data.isPrivate),
      deadlineDate: data.deadlineDate,
      scheduledDates: data.scheduledDates,
      creatorId: user._id,
    };

    const surveyInstance = new Survey(createData);
    const processedData = await SurveyImporter.process(user, surveyInstance);

    // generate new scheduled notifications from survey template
    if (this.surveyDeadlineService.validate(surveyInstance)) {
      const createdScheduledDates = await this.surveyDeadlineService.bulkCreate({ survey: surveyInstance, scheduledDates: surveyInstance.scheduledDates ?? [] });
      surveyInstance.scheduledDates = createdScheduledDates;
      await surveyInstance.save();
    }

    return processedData;
  }

  /**
   * Blocking version of survey creation used with background jobs
   **/
  public async blockingCreateSurveys(data: BulkSurveysData, user: UserModel, history: SurveyTemplateHistoryModel) {
    const initiativeUnitConfigMap = data.useInitiativeSettings
      ? await this.surveyConfigService.getInitiativeUnitConfigMap(data.reportingLevels)
      : undefined;
    await this.templateManager.batchCreateSurveys((reportingCompany) => {
      const unitConfig = this.getInitiativeUnitConfig({ reportingCompany, data, initiativeUnitConfigMap });
      return this.createSurveyFromTemplate({ user, reportingCompany, data, unitConfig });
    }, history);

    await this.notificationManager
      .sendBulkSurveysGeneratedNotification(user._id, data, history)
      .catch((e) => this.logger.error(e));

    return history;
  }

  private getInitiativeUnitConfig({
    reportingCompany,
    data,
    initiativeUnitConfigMap,
  }: {
    reportingCompany: InitiativePlain;
    data: BulkSurveysData;
    initiativeUnitConfigMap: Map<string, UnitConfig> | undefined;
  }) {
    if (!data.useInitiativeSettings) {
      return data.unitConfig;
    }

    const unitConfig = initiativeUnitConfigMap?.get(String(reportingCompany._id));

    if (!unitConfig) {
      throw new ContextError(`No unit config found for company: ${reportingCompany._id}`, {
        initiativeId: reportingCompany._id,
      });
    }
    return unitConfig;
  }
}

let instance: SurveyTemplateManager;
export const getSurveyTemplateManager = () => {
  if (!instance) {
    instance = new SurveyTemplateManager(
      getRootInitiativeService(),
      getSelfOnboardingManager(),
      wwgLogger,
      getNotificationManager(),
      getTemplateManager(),
      getSurveyDeadlineService(),
      SurveyConfigService
    );
  }

  return instance;
};
