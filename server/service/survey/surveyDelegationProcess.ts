/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  type DelegationScope,
  type DelegationScopeChildGroup,
  type DelegationScopeUsers,
  type DelegationScopeUsersGroup,
  type Scope
} from '../../models/survey';
import {
  type BaseScopeChange,
  type DelegationScopeUser,
} from './model/DelegationScope';
import {
  createStakeholderGroup,
  getStakeholderGroupKeys
} from '../stakeholder/StakeholderGroupManager';
import {
  getGroup,
  type Group,
  type GroupTypes,
} from '@g17eco/core';
import { Actions, type OptionalAction } from '../action/Actions';
import { ObjectId } from 'bson';
import { type StakeholderGroup } from '../../models/stakeholderGroup';
import { SurveyScope } from './SurveyScope';
import { toPlainObject } from '../../util/mongoose';
import { type MetricGroupWithSubgroups } from '../../models/metricGroup';
import { getSubGroup, findGroupByChildCode, getGroupChildrenTags, getGroupCode, isMetricGroup } from '../../util/groups';

export const getScopeGroup = (): DelegationScopeUsersGroup => ({
  inScope: false,
  isPartial: false,
  ...createStakeholderGroup(),
});


const extractChildrenIds = (children?: DelegationScopeUsersGroup[]): ObjectId[] => {

  if (!children) {
    return [];
  }

  const ids: ObjectId[] = [];
  for (const child of children) {
    for (const key of getStakeholderGroupKeys()) {
      const group = child[key];
      if (Array.isArray(group)) {
        ids.push(...group)
      }
    }
    if (Array.isArray(child.children)) {
      const childrenIds = extractChildrenIds(child.children)
      ids.push(...childrenIds)
    }
  }

  return ids;
};

const extractChildrenCodes = (
  role: keyof StakeholderGroup,
  userId: string,
  children?: DelegationScopeChildGroup[]
): string[] => {
  if (!children) {
    return [];
  }

  const codes: string[] = [];
  for (const child of children) {
    if (child[role].some((id: ObjectId) => String(id) === userId)) {
      codes.push(child.code);
    }
    if (Array.isArray(child.children)) {
      const childrenCodes = extractChildrenCodes(role, userId, child.children)
      codes.push(...childrenCodes)
    }
  }
  return codes;
};

export const extractSurveyIds = (delegationScope: DelegationScope) => {

  if (!delegationScope) {
    return [];
  }

  const plainDelegationScope = toPlainObject(delegationScope);

  const ids: ObjectId[] = [];
  for (const scopeTags of Object.values(plainDelegationScope)) {
    ids.push(...extractChildrenIds(Object.values(scopeTags as DelegationScopeUsers)))
  }
  return Array.from(new Set(ids.map(String))).map(id => new ObjectId(id))
}

export const createChildGroup = (code: string): DelegationScopeChildGroup => {
  return { ...getScopeGroup(), code }
}

export const processUserUpdate = (action: OptionalAction, userIds: ObjectId[], userId: string) => {
  const ids = new Set(userIds.map(String))
  if (action === Actions.Add) {
    ids.add(userId);
  } else if (action === Actions.Remove) {
    ids.delete(userId);
  }

  return Array.from(ids).map(id => new ObjectId(id));
}

// Requires plain delegation scope, not mongoose object
export const fromDelegationScope = (
  delegationScope: DelegationScope,
  role: keyof StakeholderGroup,
  userId: ObjectId,
): Scope<string | ObjectId> => {

  const scope = SurveyScope.createEmpty();
  const userIdString = String(userId);

  Object.entries(delegationScope).forEach((scopeSection) => {
    const [scopeType, scopes] = scopeSection as [keyof DelegationScope, DelegationScopeUsers]
    Object.entries(scopes).forEach(([scopeTag, group]: [string, DelegationScopeUsersGroup]) => {
      if (scopeType === 'custom') {
        if (ObjectId.isValid(scopeTag) && group[role].some((id: ObjectId) => String(id) === userIdString)) {
          // Keys are actually objectIds, need to convert it back
          scope[scopeType].push(new ObjectId(scopeTag));
        }
        return;
      }
      if (group[role].some((id: ObjectId) => String(id) === userIdString)) {
        scope[scopeType].push(scopeTag);
      }
      const codes = extractChildrenCodes(role, userIdString, group.children)
      scope[scopeType].push(...codes);
    })
  })

  return scope;
}

const isChildrenEmpty = (children?: DelegationScopeUsersGroup[]): boolean => {

  if (!children) {
    return true;
  }

  for (const child of children) {
    for (const key of getStakeholderGroupKeys()) {
      const group = child[key];
      if (Array.isArray(group) && group.length > 0) {
        return false;
      }
    }
    if (!isChildrenEmpty(child.children)) {
      return false;
    }
  }

  return true
};

export const isDelegationScopeEmpty = (delegationScope?: DelegationScope) => {
  if (!delegationScope) {
    return true;
  }

  // Need to be plain object for property iteration, not mongoose object
  const plainDelegationScope = toPlainObject(delegationScope);

  for (const scopeTags of Object.values(plainDelegationScope) as DelegationScopeUsers[]) {
    // sdg, standards, materiality, frameworks scope tags
    if (!isChildrenEmpty(Object.values(scopeTags))) {
      return false;
    }
  }

  return true;
}

interface ProcessChildParams {
  codeGroup: DelegationScopeUsersGroup;
  scopeTags: string[];
  group: Group | MetricGroupWithSubgroups;
  role: keyof StakeholderGroup;
  action: Actions;
  userId: string;
  applyByParent: boolean;
}

const getChildGroup = (codeGroup: DelegationScopeUsersGroup, subGroup: Group | MetricGroupWithSubgroups): DelegationScopeChildGroup => {

  if (!codeGroup.children) {
    codeGroup.children = [];
  }

  const groupCode = getGroupCode(subGroup);
  const childGroup = codeGroup.children.find(g => g.code === groupCode);
  if (childGroup) {
    return childGroup;
  }

  const newChildGroup = createChildGroup(groupCode);
  codeGroup.children.push(newChildGroup)
  return newChildGroup;
};

const processChild = (processData: ProcessChildParams) => {

  const { codeGroup, scopeTags, group, role, action, userId, applyByParent } = processData;
  const codesToProcess = applyByParent ? group.subgroups?.map((g) => getGroupCode(g)) ?? [] : scopeTags;

  if (codesToProcess.length > 0 && !codeGroup.children) {
    codeGroup.children = [];
  }

  for (const tag of codesToProcess) {
    let subGroup = getSubGroup(group, tag);
    let isPartial = false;
    if (!subGroup) {
      subGroup = findGroupByChildCode(group, tag);
      isPartial = true;
    }

    if (!subGroup) {
      continue;
    }

    const childGroup = getChildGroup(codeGroup, subGroup);

    if (!isPartial) {
      childGroup[role] = processUserUpdate(action, childGroup[role], userId);
    }
    if (subGroup.subgroups) {
      const validTags = isMetricGroup(subGroup)
        ? getGroupChildrenTags(subGroup).map(String)
        : getGroupChildrenTags(subGroup);
      // Fill all inner data
      processChild({
        ...processData,
        scopeTags: codesToProcess.filter(c => validTags.includes(c)),
        codeGroup: childGroup,
        group: subGroup,
      })
    }
  }
};

export const processScopeGroups = (delegationScope: DelegationScope, delegationRequest: DelegationScopeUser) => {
  const {
    role,
    scopeGroups = [],
    action,
    userId
  } = delegationRequest;

  for (const { code, scopeTags = [], scopeType } of scopeGroups) {
    if (!delegationScope[scopeType]) {
      delegationScope[scopeType] = {};
    }

    if (!delegationScope[scopeType][code]) {
      delegationScope[scopeType][code] = createStakeholderGroup();
    }
    const codeGroup = delegationScope[scopeType][code];
    const applyByParent = scopeTags.length === 0
    if (applyByParent) {
      codeGroup[role] = processUserUpdate(action, codeGroup[role], userId);
    }

    // Handle children
    const group = getGroup(scopeType as GroupTypes, code);
    if (!group) {
      continue;
    }

    processChild({
      codeGroup,
      scopeTags,
      group,
      role,
      action,
      userId,
      applyByParent
    });
  }
  return delegationScope;
}


interface ApplyScopeData {
  delegationScope: DelegationScope & { toObject?: () => DelegationScope },
  surveyId: ObjectId,
  surveyDelegationScope: DelegationScope,
  obUserId: string
}

export function applyOnboardingDelegationScope(data: ApplyScopeData) {
  const {
    delegationScope,
    surveyId,
    surveyDelegationScope: surveyDS,
    obUserId
  } = data;

  let hasChanged = false;

  // Need to be plain object for property iteration, not mongoose object
  const plainDelegationScope = delegationScope?.toObject ?
    delegationScope.toObject() : delegationScope;

  // Update surveys delegationScope
  for (const [type, scopeTags] of Object.entries(plainDelegationScope)) {

    const t = type as keyof DelegationScope;
    // sdg, standards, materiality, frameworks scope tags
    for (const [scopeTag, stakeholderGroup] of Object.entries(scopeTags) as [string, DelegationScopeUsersGroup][]) {
      // stakeholder | verifier | escalation
      for (const role of getStakeholderGroupKeys()) {
        const userIds = stakeholderGroup[role];
        if (!Array.isArray(userIds) || userIds.length === 0) {
          continue;
        }

        for (const id of userIds) {
          const r = role;
          if (!id.equals(surveyId) || surveyDS[t][scopeTag]?.[r].some((userId: ObjectId) => userId.equals(id))) {
            continue;
          }

          // Update
          if (!surveyDS[t][scopeTag]) {
            surveyDS[t][scopeTag] = getScopeGroup();
          }
          surveyDS[t][scopeTag][r] = processUserUpdate(
            Actions.Add,
            surveyDS[t][scopeTag][r],
            obUserId
          );
          hasChanged = true;
        }
      }

      if (stakeholderGroup.children) {

        const group = getGroup(type as GroupTypes, scopeTag);
        if (!group) {
          continue;
        }

        const children: DelegationScopeChildGroup[] = stakeholderGroup.children
        for (const child of children) {
          for (const role of getStakeholderGroupKeys()) {
            const userIds = child[role];
            for (const id of userIds) {

              // Wrong survey
              if (!id.equals(surveyId)) {
                continue;
              }

              // Is already there?
              const code = child.code;

              if (!surveyDS[t][scopeTag]) {
                surveyDS[t][scopeTag] = getScopeGroup();
              }

              const surveyChildElement = surveyDS[t][scopeTag].children?.find(c => c.code === code);
              if (surveyChildElement?.[role]?.some((userId: ObjectId) => userId.equals(id))) {
                continue;
              }

              const subGroup = getSubGroup(group, code);
              if (subGroup) {
                const childGroup = getChildGroup(surveyDS[t][scopeTag], subGroup);
                childGroup[role] = processUserUpdate('add', childGroup[role], obUserId);
                hasChanged = true;
              }
            }
          }
        }
      }
    }
  }

  return { hasChanged, surveyDelegationScope: surveyDS };
}

export const applyScopeAction = <T = string>(
  scopeTypeData: T[] = [],
  updateData: T[],
  action: Actions,
): T[] => {

  if (action === Actions.Remove) {
    const removeCodes = new Set(updateData.map(String));
    return scopeTypeData.filter(code => !removeCodes.has(String(code)));
  }

  const addCodes = new Set(scopeTypeData.map(String));
  updateData.forEach(code => {
    if (!addCodes.has(String(code))) {
      scopeTypeData.push(code);
    }
  });

  return scopeTypeData;
}

interface ProcessScopeChildParams {
  currentScopeTags: string[];
  scopeTags: string[];
  group: Group | MetricGroupWithSubgroups;
  action: Actions;
  applyParent?: boolean,
  parentInScope: boolean;
}

interface ChangeResult {
  data: string[],
  applyParent: boolean
}

interface CurrentLevelChangeParams {
  group: Group | MetricGroupWithSubgroups;
  subGroup: Group | MetricGroupWithSubgroups;
  result: ChangeResult;
  action: Actions;
  parentInScope: boolean;
}

const applyCurrentLevelChange = ({ group, subGroup, result, action, parentInScope }: CurrentLevelChangeParams): ChangeResult => {

  if (Actions.Remove !== action) {
    return result;
  }

  if (parentInScope && group.subgroups) {
    // [gri], remove [gri-1-102], all other branch should be included
    // Ensure siblings remain in scope, by removing parent (legacy setup)
    const siblingChildrenTags = group.subgroups
      .filter((g) => getGroupCode(g) !== getGroupCode(subGroup))
      .map((g) => {
        const childrenTags = isMetricGroup(g)
          ? getGroupChildrenTags(g).map(String)
          : getGroupChildrenTags(g);
        return [getGroupCode(g), ...childrenTags];
      })
      .flat();

    if (siblingChildrenTags.length > 0) {
      result.data = applyScopeAction(result.data, siblingChildrenTags, Actions.Add)
    }
  }

  return {
    data: applyScopeAction(result.data, [getGroupCode(subGroup)], action),
    applyParent: result.applyParent,
  };
};

const processChildScopeChange = (processData: ProcessScopeChildParams): ChangeResult => {

  const { scopeTags, group, action, parentInScope } = processData;
  let { currentScopeTags, applyParent = false } = processData;

  for (const tag of scopeTags) {
    let subGroup = getSubGroup(group, tag);
    let isPartial = false;
    if (!subGroup) {
      subGroup = findGroupByChildCode(group, tag);
      isPartial = true;
    }

    if (!subGroup) {
      continue;
    }

    applyParent = applyParent || Actions.Remove === action;

    if (!isPartial) {
      const childrenTags = isMetricGroup(subGroup)
        ? getGroupChildrenTags(subGroup).map(String)
        : getGroupChildrenTags(subGroup);
      const allCodes = [getGroupCode(subGroup), ...childrenTags];
      currentScopeTags = applyScopeAction(currentScopeTags, allCodes, action);

      currentScopeTags = applyCurrentLevelChange({
        group,
        subGroup,
        result: { data: currentScopeTags, applyParent },
        action,
        parentInScope,
      }).data;

      continue;
    }

    if (subGroup.subgroups) {
      const validTags = isMetricGroup(subGroup)
        ? getGroupChildrenTags(subGroup).map(String)
        : getGroupChildrenTags(subGroup);
      // Fill all inner data
      const isParentInScope = parentInScope || currentScopeTags.includes(getGroupCode(subGroup));
      const result = processChildScopeChange({
        scopeTags: scopeTags.filter(c => validTags.includes(c)),
        currentScopeTags,
        group: subGroup,
        action,
        parentInScope: isParentInScope,
      })
      const r = applyCurrentLevelChange({ group, subGroup, result, action, parentInScope });
      currentScopeTags = r.data;
      applyParent = r.applyParent;
    }
  }

  return { data: currentScopeTags, applyParent };
};

function processEmptyScopeTags(
  scope: Scope,
  scopeType: 'custom',
  group: MetricGroupWithSubgroups,
  action: Actions
): void
function processEmptyScopeTags(
  scope: Scope,
  scopeType: Exclude<keyof Scope, 'custom'>,
  group: Group,
  action: Actions
): void
function processEmptyScopeTags(
  scope: Scope,
  scopeType: keyof Scope,
  group: Group | MetricGroupWithSubgroups,
  action: Actions
): void {
  const groupCode = getGroupCode(group);
  if (scopeType === 'custom') {
    const allIds = getGroupChildrenTags(group);
    // scope[scopeType] = applyScopeAction(scope[scopeType], allIds, action);
    return;
  }
  const allCodes = [groupCode, ...getGroupChildrenTags(group as Group)];
  scope[scopeType] = applyScopeAction(scope[scopeType], allCodes, action);
}

const processChildScopeTags = (params: {
  scope: Scope;
  scopeType: keyof Scope;
  code: string;
  scopeTags: string[];
  group: Group | MetricGroupWithSubgroups;
  action: Actions;
}): void => {
  const { scope, scopeType, code, scopeTags, group, action } = params;
  const currentScopeTags = scopeType === 'custom' ? scope[scopeType].map(String) : scope[scopeType];
  const parentInScope = currentScopeTags.includes(code);

  const { data, applyParent } = processChildScopeChange({
    currentScopeTags,
    scopeTags,
    group,
    action,
    parentInScope,
  });

  const groupCode = getGroupCode(group);
  const updatedCodes = applyParent ? applyScopeAction(data, [groupCode], action) : data;
  if (scopeType === 'custom') {
    scope[scopeType] = updatedCodes.map((_id) => new ObjectId(_id));
    return;
  }
  scope[scopeType] = updatedCodes;
};

export const processScopeChange = (params: {
  data: BaseScopeChange;
  surveyScope?: Scope;
  metricGroups?: MetricGroupWithSubgroups[];
}): Scope => {
  const { data, surveyScope, metricGroups } = params;
  const { scopeGroups, action } = data;
  const scope = surveyScope ?? SurveyScope.createEmpty();

  if (!scopeGroups || scopeGroups.length === 0) {
    return scope;
  }

  for (const { code, scopeTags = [], scopeType } of scopeGroups) {

    if (!scope[scopeType]) {
      scope[scopeType] = [];
    }

    if (scopeType === 'custom') {
      // metricGroups is not passed, allow to add custom group _id like before
      if (!metricGroups) {
        scope.custom = applyScopeAction(scope.custom, [new ObjectId(code)], action);
        continue;
      }
      const metricGroup = metricGroups.find((g) => String(g._id) === code);
      if (!metricGroup) {
        continue;
      }

      if (scopeTags.length === 0) {
        processEmptyScopeTags(scope, scopeType, metricGroup, action);
      } else {
        processChildScopeTags({ scope, scopeType, code, scopeTags, group: metricGroup, action });
      }
      continue;
    }

    if (scopeType === 'materiality') {
      scope.materiality = applyScopeAction(scope.materiality, [code], action);
      continue;
    }

    const group = getGroup(scopeType as GroupTypes, code);
    if (!group) {
      continue;
    }

    if (scopeTags.length === 0) {
      processEmptyScopeTags(scope, scopeType, group, action);
    } else {
      processChildScopeTags({ scope, scopeType, code, scopeTags, group, action });
    }
  }

  return scope;
};
