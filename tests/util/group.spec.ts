import {
  findGroupByChildCode as findGroupByChildCodeCore,
  getGroup,
  getGroupChildrenTags as getGroupChildrenTagsCore,
} from '@g17eco/core';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import { type MetricGroupWithSubgroups } from '../../server/models/metricGroup';
import { findGroupByChildCode, getGroupChildrenTags } from '../../server/util/groups';
import { createMetricGroup } from '../fixtures/metricGroupFixtures';

describe.only('groups', () => {
  const [groupId, childGroupOneId, grandChildGroupOneId, childGroupTwoId] = Array.from({ length: 4 }).map(
    () => new ObjectId()
  );
  const group = createMetricGroup({ _id: groupId });
  const childGroupOne = createMetricGroup({ _id: childGroupOneId, parentId: groupId });
  const grandChildGroupOne = createMetricGroup({ _id: grandChildGroupOneId, parentId: childGroupOneId });
  const childGroupTwo = createMetricGroup({ _id: childGroupTwoId, parentId: groupId });

  const metricGroup: MetricGroupWithSubgroups = {
    ...group,
    subgroups: [{ ...childGroupOne, subgroups: [grandChildGroupOne] }, childGroupTwo],
  };

  describe('findGroupByChildCode ', () => {
    describe('standards-and-frameworks', () => {
      it('should find group by child code - first subgroup level', () => {
        const groupCode = 'gri';
        const subGroupCode = 'gri-1';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          const expectedSubGroup = findGroupByChildCodeCore(group, subGroupCode);
          expect(findGroupByChildCode(group, subGroupCode)).to.be.eq(expectedSubGroup); // gri-1
        }
      });
      it('should find group by child code - second subgroup level', () => {
        const groupCode = 'gri';
        const subGroupCode = 'gri-1-102';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          const expectedSubGroup = findGroupByChildCodeCore(group, subGroupCode);
          expect(findGroupByChildCode(group, subGroupCode)).to.be.eq(expectedSubGroup);  // gri-1
        }
      });
    });

    describe('metric-groups', () => {
      it('should return first level subgroup when find by first subgroup level', () => {
        const found = findGroupByChildCode(metricGroup, String(childGroupOneId));
        expect(found?._id).to.deep.eq(childGroupOne._id);
        expect(found?.groupName).to.be.eq(childGroupOne.groupName);
      });
      it('should return first level subgroup when find by second subgroup level', () => {
        const found = findGroupByChildCode(metricGroup, String(grandChildGroupOneId));
        expect(found?._id).to.deep.eq(childGroupOne._id);
        expect(found?.groupName).to.be.eq(childGroupOne.groupName);
      });
    });
  });

  describe('getGroupChildrenTags', () => {
    describe('standards-and-frameworks', () => {
      it('should return all children tags', () => {
        const groupCode = 'gri';
        const group = getGroup('standards-and-frameworks', groupCode);
        if (group) {
          const expectedTags = getGroupChildrenTagsCore(group);
          expect(getGroupChildrenTags(group)).to.deep.eq(expectedTags);
        }
      });
    });
    describe('metric-groups', () => {
      it('should return all children tags', () => {
        const expectedTags = [childGroupOneId, grandChildGroupOneId, childGroupTwoId].map(String);
        expect(getGroupChildrenTags(metricGroup)).to.deep.eq(expectedTags);
      });
    });
  });
});
